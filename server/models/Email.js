const { query, transaction } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Email {

  // 创建邮件
  static async create(emailData) {
    const {
      message_id,
      from_address,
      to_address,
      subject,
      sent_time,
      received_time,
      html_content,
      verification_code
    } = emailData;

    // 验证必填字段
    if (!message_id || !from_address || !to_address || !received_time) {
      throw new Error('缺少必填字段：message_id, from_address, to_address, received_time');
    }

    const id = uuidv4();
    const now = new Date();

    const sql = `
      INSERT INTO emails (
        id, message_id, from_address, to_address, subject, sent_time, 
        received_time, html_content, verification_code, created_time, updated_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      message_id,
      from_address,
      to_address,
      subject || null,
      sent_time || null,
      received_time,
      html_content || null,
      verification_code || null,
      now,
      now
    ];

    try {
      await query(sql, params);
      return {
        id,
        message_id,
        from_address,
        to_address,
        subject,
        sent_time,
        received_time,
        html_content,
        verification_code,
        created_time: now,
        updated_time: now
      };
    } catch (error) {
      console.error('创建邮件失败:', error.message);
      throw error;
    }
  }

  // 根据ID查找邮件
  static async findById(id) {
    const sql = `
      SELECT id, message_id, from_address, to_address, subject, sent_time, received_time, 
             html_content, verification_code, created_time, updated_time
      FROM emails 
      WHERE id = ?
    `;

    try {
      const emails = await query(sql, [id]);
      return emails[0] || null;
    } catch (error) {
      console.error('查找邮件失败:', error.message);
      throw error;
    }
  }

  // 根据message_id查找邮件
  static async findByMessageId(messageId) {
    const sql = `
      SELECT id, message_id, from_address, to_address, subject, sent_time, received_time, 
             html_content, verification_code, created_time, updated_time
      FROM emails 
      WHERE message_id = ?
    `;

    try {
      const emails = await query(sql, [messageId]);
      return emails[0] || null;
    } catch (error) {
      console.error('根据message_id查找邮件失败:', error.message);
      throw error;
    }
  }

  // 获取邮件列表（支持分页和筛选）
  static async findAll(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      fromAddress,
      toAddress,
      subject,
      verificationCode,
      startTime,
      endTime,
      sortBy = 'received_time',
      sortOrder = 'DESC'
    } = options;

    try {
      // 构建查询条件
      let whereConditions = [];
      let queryParams = [];

      if (fromAddress) {
        whereConditions.push('from_address LIKE ?');
        queryParams.push(`%${fromAddress}%`);
      }

      if (toAddress) {
        whereConditions.push('to_address LIKE ?');
        queryParams.push(`%${toAddress}%`);
      }

      if (subject) {
        whereConditions.push('subject LIKE ?');
        queryParams.push(`%${subject}%`);
      }

      if (verificationCode) {
        whereConditions.push('verification_code LIKE ?');
        queryParams.push(`%${verificationCode}%`);
      }

      if (startTime) {
        whereConditions.push('received_time >= ?');
        queryParams.push(startTime);
      }

      if (endTime) {
        whereConditions.push('received_time <= ?');
        queryParams.push(endTime);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 验证排序字段
      const allowedSortFields = ['received_time', 'sent_time', 'from_address', 'to_address', 'subject', 'created_time'];
      const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'received_time';
      const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM emails ${whereClause}`;
      const countResult = await query(countSql, queryParams);
      const total = countResult[0].total;

      // 计算分页
      const pageNum = Math.max(1, parseInt(page));
      const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize)));
      const offset = (pageNum - 1) * pageSizeNum;

      // 获取邮件列表
      const listSql = `
        SELECT id, message_id, from_address, to_address, subject, sent_time, received_time, 
               verification_code, created_time, updated_time
        FROM emails 
        ${whereClause}
        ORDER BY ${validSortBy} ${validSortOrder}
        LIMIT ? OFFSET ?
      `;
      
      const listParams = [...queryParams, pageSizeNum, offset];
      const emails = await query(listSql, listParams);

      return {
        list: emails,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages: Math.ceil(total / pageSizeNum)
        }
      };
    } catch (error) {
      console.error('获取邮件列表失败:', error.message);
      throw error;
    }
  }

  // 更新邮件
  static async update(id, updateData) {
    const {
      from_address,
      to_address,
      subject,
      sent_time,
      received_time,
      html_content,
      verification_code
    } = updateData;

    try {
      // 检查邮件是否存在
      const existingEmail = await this.findById(id);
      if (!existingEmail) {
        return null;
      }

      // 构建更新字段
      const updateFields = [];
      const updateParams = [];

      if (from_address !== undefined) {
        updateFields.push('from_address = ?');
        updateParams.push(from_address);
      }

      if (to_address !== undefined) {
        updateFields.push('to_address = ?');
        updateParams.push(to_address);
      }

      if (subject !== undefined) {
        updateFields.push('subject = ?');
        updateParams.push(subject);
      }

      if (sent_time !== undefined) {
        updateFields.push('sent_time = ?');
        updateParams.push(sent_time);
      }

      if (received_time !== undefined) {
        updateFields.push('received_time = ?');
        updateParams.push(received_time);
      }

      if (html_content !== undefined) {
        updateFields.push('html_content = ?');
        updateParams.push(html_content);
      }

      if (verification_code !== undefined) {
        updateFields.push('verification_code = ?');
        updateParams.push(verification_code);
      }

      if (updateFields.length === 0) {
        throw new Error('没有提供要更新的字段');
      }

      // 添加更新时间
      updateFields.push('updated_time = ?');
      updateParams.push(new Date());
      updateParams.push(id);

      const sql = `UPDATE emails SET ${updateFields.join(', ')} WHERE id = ?`;
      await query(sql, updateParams);

      // 返回更新后的邮件信息
      return await this.findById(id);
    } catch (error) {
      console.error('更新邮件失败:', error.message);
      throw error;
    }
  }

  // 删除邮件
  static async delete(id) {
    try {
      // 检查邮件是否存在
      const existingEmail = await this.findById(id);
      if (!existingEmail) {
        return false;
      }

      const sql = 'DELETE FROM emails WHERE id = ?';
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除邮件失败:', error.message);
      throw error;
    }
  }

  // 批量删除邮件
  static async deleteMany(ids) {
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw new Error('请提供要删除的邮件ID数组');
    }

    try {
      // 验证ID格式
      const validIds = ids.filter(id => id && typeof id === 'string');
      if (validIds.length === 0) {
        throw new Error('没有有效的邮件ID');
      }

      // 检查有多少邮件存在
      const placeholders = validIds.map(() => '?').join(',');
      const existingSql = `SELECT id FROM emails WHERE id IN (${placeholders})`;
      const existingEmails = await query(existingSql, validIds);
      
      if (existingEmails.length === 0) {
        return {
          deletedCount: 0,
          requestedCount: validIds.length,
          existingCount: 0
        };
      }

      // 批量删除邮件
      const deleteSql = `DELETE FROM emails WHERE id IN (${placeholders})`;
      const result = await query(deleteSql, validIds);
      
      const deletedCount = result.affectedRows || existingEmails.length;
      
      return {
        deletedCount,
        requestedCount: validIds.length,
        existingCount: existingEmails.length
      };
    } catch (error) {
      console.error('批量删除邮件失败:', error.message);
      throw error;
    }
  }

  // 搜索邮件（支持全文搜索）
  static async search(options = {}) {
    const {
      keyword,
      page = 1,
      pageSize = 10,
      sortBy = 'received_time',
      sortOrder = 'DESC'
    } = options;

    if (!keyword || keyword.trim() === '') {
      throw new Error('搜索关键词不能为空');
    }

    try {
      const searchKeyword = `%${keyword.trim()}%`;

      // 验证排序字段
      const allowedSortFields = ['received_time', 'sent_time', 'from_address', 'to_address', 'subject', 'created_time'];
      const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'received_time';
      const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

      // 获取搜索结果总数
      const countSql = `
        SELECT COUNT(*) as total
        FROM emails
        WHERE from_address LIKE ?
           OR to_address LIKE ?
           OR subject LIKE ?
           OR verification_code LIKE ?
      `;
      const countResult = await query(countSql, [searchKeyword, searchKeyword, searchKeyword, searchKeyword]);
      const total = countResult[0].total;

      // 计算分页
      const pageNum = Math.max(1, parseInt(page));
      const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize)));
      const offset = (pageNum - 1) * pageSizeNum;

      // 获取搜索结果
      const searchSql = `
        SELECT id, message_id, from_address, to_address, subject, sent_time, received_time,
               verification_code, created_time, updated_time
        FROM emails
        WHERE from_address LIKE ?
           OR to_address LIKE ?
           OR subject LIKE ?
           OR verification_code LIKE ?
        ORDER BY ${validSortBy} ${validSortOrder}
        LIMIT ? OFFSET ?
      `;

      const searchParams = [searchKeyword, searchKeyword, searchKeyword, searchKeyword, pageSizeNum, offset];
      const emails = await query(searchSql, searchParams);

      return {
        list: emails,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages: Math.ceil(total / pageSizeNum)
        },
        keyword
      };
    } catch (error) {
      console.error('搜索邮件失败:', error.message);
      throw error;
    }
  }

  // 获取验证码邮件列表
  static async findVerificationEmails(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      fromAddress,
      sortBy = 'received_time',
      sortOrder = 'DESC'
    } = options;

    try {
      // 构建查询条件
      let whereConditions = ['verification_code IS NOT NULL', 'verification_code != ""'];
      let queryParams = [];

      if (fromAddress) {
        whereConditions.push('from_address LIKE ?');
        queryParams.push(`%${fromAddress}%`);
      }

      const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

      // 验证排序字段
      const allowedSortFields = ['received_time', 'sent_time', 'from_address', 'verification_code'];
      const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'received_time';
      const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM emails ${whereClause}`;
      const countResult = await query(countSql, queryParams);
      const total = countResult[0].total;

      // 计算分页
      const pageNum = Math.max(1, parseInt(page));
      const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize)));
      const offset = (pageNum - 1) * pageSizeNum;

      // 获取验证码邮件列表
      const listSql = `
        SELECT id, message_id, from_address, to_address, subject, sent_time, received_time,
               verification_code, created_time, updated_time
        FROM emails
        ${whereClause}
        ORDER BY ${validSortBy} ${validSortOrder}
        LIMIT ? OFFSET ?
      `;

      const listParams = [...queryParams, pageSizeNum, offset];
      const emails = await query(listSql, listParams);

      return {
        list: emails,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages: Math.ceil(total / pageSizeNum)
        }
      };
    } catch (error) {
      console.error('获取验证码邮件列表失败:', error.message);
      throw error;
    }
  }

  // 获取邮件统计信息
  static async getStats() {
    try {
      // 获取总邮件数
      const totalSql = 'SELECT COUNT(*) as total FROM emails';
      const totalResult = await query(totalSql);
      const total = totalResult[0].total;

      // 获取今日邮件数
      const todaySql = `
        SELECT COUNT(*) as today
        FROM emails
        WHERE DATE(received_time) = CURDATE()
      `;
      const todayResult = await query(todaySql);
      const today = todayResult[0].today;

      // 获取本周邮件数
      const weekSql = `
        SELECT COUNT(*) as week
        FROM emails
        WHERE YEARWEEK(received_time, 1) = YEARWEEK(CURDATE(), 1)
      `;
      const weekResult = await query(weekSql);
      const week = weekResult[0].week;

      // 获取本月邮件数
      const monthSql = `
        SELECT COUNT(*) as month
        FROM emails
        WHERE YEAR(received_time) = YEAR(CURDATE())
        AND MONTH(received_time) = MONTH(CURDATE())
      `;
      const monthResult = await query(monthSql);
      const month = monthResult[0].month;

      // 获取有验证码的邮件数
      const verificationSql = `
        SELECT COUNT(*) as verification
        FROM emails
        WHERE verification_code IS NOT NULL AND verification_code != ''
      `;
      const verificationResult = await query(verificationSql);
      const verification = verificationResult[0].verification;

      // 获取最近7天的邮件趋势
      const trendSql = `
        SELECT
          DATE(received_time) as date,
          COUNT(*) as count
        FROM emails
        WHERE received_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(received_time)
        ORDER BY date DESC
      `;
      const trendResult = await query(trendSql);

      // 获取发件人统计（前10）
      const senderSql = `
        SELECT
          from_address,
          COUNT(*) as count
        FROM emails
        GROUP BY from_address
        ORDER BY count DESC
        LIMIT 10
      `;
      const senderResult = await query(senderSql);

      return {
        overview: {
          total,
          today,
          week,
          month,
          verification
        },
        trend: trendResult,
        topSenders: senderResult
      };
    } catch (error) {
      console.error('获取邮件统计失败:', error.message);
      throw error;
    }
  }
}

module.exports = Email;
