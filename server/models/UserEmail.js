const { query, transaction } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class UserEmail {

  // 创建用户邮箱关联
  static async create(userEmailData) {
    const {
      user_id,
      email_address,
      email_type = 'secondary',
      is_verified = false,
      is_active = true,
      verification_token,
      description
    } = userEmailData;

    // 验证必填字段
    if (!user_id || !email_address) {
      throw new Error('缺少必填字段：user_id, email_address');
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email_address)) {
      throw new Error('邮箱格式不正确');
    }

    const id = uuidv4();
    const now = new Date();

    const sql = `
      INSERT INTO user_emails (
        id, user_id, email_address, email_type, is_verified, is_active,
        verification_token, description, created_time, updated_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      user_id,
      email_address.toLowerCase().trim(),
      email_type,
      is_verified ? 1 : 0,
      is_active ? 1 : 0,
      verification_token || null,
      description || null,
      now,
      now
    ];

    try {
      await query(sql, params);
      return {
        id,
        user_id,
        email_address: email_address.toLowerCase().trim(),
        email_type,
        is_verified,
        is_active,
        verification_token,
        description,
        created_time: now,
        updated_time: now
      };
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('该用户已关联此邮箱地址');
      }
      console.error('创建用户邮箱关联失败:', error.message);
      throw error;
    }
  }

  // 根据ID查找用户邮箱
  static async findById(id) {
    const sql = `
      SELECT ue.*, u.username, u.email as user_primary_email
      FROM user_emails ue
      LEFT JOIN user u ON ue.user_id = u.id
      WHERE ue.id = ?
    `;

    try {
      const userEmails = await query(sql, [id]);
      return userEmails[0] || null;
    } catch (error) {
      console.error('查找用户邮箱失败:', error.message);
      throw error;
    }
  }

  // 根据用户ID获取所有邮箱
  static async findByUserId(userId, options = {}) {
    const { includeInactive = false } = options;

    let whereConditions = ['ue.user_id = ?'];
    let queryParams = [userId];

    if (!includeInactive) {
      whereConditions.push('ue.is_active = 1');
    }

    const sql = `
      SELECT ue.*, u.username, u.email as user_primary_email
      FROM user_emails ue
      LEFT JOIN user u ON ue.user_id = u.id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY 
        CASE ue.email_type 
          WHEN 'primary' THEN 1 
          WHEN 'work' THEN 2 
          WHEN 'personal' THEN 3 
          WHEN 'secondary' THEN 4 
          ELSE 5 
        END,
        ue.created_time ASC
    `;

    try {
      const userEmails = await query(sql, queryParams);
      return userEmails;
    } catch (error) {
      console.error('获取用户邮箱列表失败:', error.message);
      throw error;
    }
  }

  // 根据邮箱地址查找
  static async findByEmailAddress(emailAddress) {
    const sql = `
      SELECT ue.*, u.username, u.email as user_primary_email
      FROM user_emails ue
      LEFT JOIN user u ON ue.user_id = u.id
      WHERE ue.email_address = ? AND ue.is_active = 1
    `;

    try {
      const userEmails = await query(sql, [emailAddress.toLowerCase().trim()]);
      return userEmails[0] || null;
    } catch (error) {
      console.error('根据邮箱地址查找失败:', error.message);
      throw error;
    }
  }

  // 获取用户邮箱列表（支持分页）
  static async findAll(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      userId,
      emailType,
      isVerified,
      isActive,
      searchKeyword
    } = options;

    let whereConditions = [];
    let queryParams = [];

    if (userId) {
      whereConditions.push('ue.user_id = ?');
      queryParams.push(userId);
    }

    if (emailType) {
      whereConditions.push('ue.email_type = ?');
      queryParams.push(emailType);
    }

    if (isVerified !== undefined) {
      whereConditions.push('ue.is_verified = ?');
      queryParams.push(isVerified ? 1 : 0);
    }

    if (isActive !== undefined) {
      whereConditions.push('ue.is_active = ?');
      queryParams.push(isActive ? 1 : 0);
    }

    if (searchKeyword) {
      whereConditions.push('(ue.email_address LIKE ? OR u.username LIKE ? OR ue.description LIKE ?)');
      const keyword = `%${searchKeyword}%`;
      queryParams.push(keyword, keyword, keyword);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    try {
      // 获取总数
      const countSql = `
        SELECT COUNT(*) as total
        FROM user_emails ue
        LEFT JOIN user u ON ue.user_id = u.id
        ${whereClause}
      `;
      const countResult = await query(countSql, queryParams);
      const total = countResult[0].total;

      // 计算分页
      const pageNum = Math.max(1, parseInt(page));
      const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize)));
      const offset = (pageNum - 1) * pageSizeNum;

      // 获取列表
      const listSql = `
        SELECT ue.*, u.username, u.email as user_primary_email
        FROM user_emails ue
        LEFT JOIN user u ON ue.user_id = u.id
        ${whereClause}
        ORDER BY ue.created_time DESC
        LIMIT ? OFFSET ?
      `;

      const listParams = [...queryParams, pageSizeNum, offset];
      const userEmails = await query(listSql, listParams);

      return {
        list: userEmails,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages: Math.ceil(total / pageSizeNum)
        }
      };
    } catch (error) {
      console.error('获取用户邮箱列表失败:', error.message);
      throw error;
    }
  }

  // 更新用户邮箱
  static async update(id, updateData) {
    const {
      email_type,
      is_verified,
      is_active,
      verification_token,
      verified_at,
      description
    } = updateData;

    try {
      // 检查是否存在
      const existingUserEmail = await this.findById(id);
      if (!existingUserEmail) {
        return null;
      }

      // 构建更新字段
      const updateFields = [];
      const updateParams = [];

      if (email_type !== undefined) {
        updateFields.push('email_type = ?');
        updateParams.push(email_type);
      }

      if (is_verified !== undefined) {
        updateFields.push('is_verified = ?');
        updateParams.push(is_verified ? 1 : 0);
        
        // 如果设置为已验证，同时更新验证时间
        if (is_verified && !verified_at) {
          updateFields.push('verified_at = ?');
          updateParams.push(new Date());
        }
      }

      if (is_active !== undefined) {
        updateFields.push('is_active = ?');
        updateParams.push(is_active ? 1 : 0);
      }

      if (verification_token !== undefined) {
        updateFields.push('verification_token = ?');
        updateParams.push(verification_token);
      }

      if (verified_at !== undefined) {
        updateFields.push('verified_at = ?');
        updateParams.push(verified_at);
      }

      if (description !== undefined) {
        updateFields.push('description = ?');
        updateParams.push(description);
      }

      if (updateFields.length === 0) {
        throw new Error('没有提供要更新的字段');
      }

      // 添加更新时间
      updateFields.push('updated_time = ?');
      updateParams.push(new Date());
      updateParams.push(id);

      const sql = `UPDATE user_emails SET ${updateFields.join(', ')} WHERE id = ?`;
      await query(sql, updateParams);

      // 返回更新后的数据
      return await this.findById(id);
    } catch (error) {
      console.error('更新用户邮箱失败:', error.message);
      throw error;
    }
  }

  // 删除用户邮箱关联
  static async delete(id) {
    try {
      // 检查是否存在
      const existingUserEmail = await this.findById(id);
      if (!existingUserEmail) {
        return false;
      }

      const sql = 'DELETE FROM user_emails WHERE id = ?';
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除用户邮箱关联失败:', error.message);
      throw error;
    }
  }

  // 更新邮件统计信息
  static async updateEmailStats(emailAddress) {
    const sql = `
      UPDATE user_emails 
      SET 
        email_count = email_count + 1,
        last_email_received_at = ?,
        updated_time = ?
      WHERE email_address = ? AND is_active = 1
    `;

    const now = new Date();
    try {
      const result = await query(sql, [now, now, emailAddress.toLowerCase().trim()]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新邮件统计失败:', error.message);
      throw error;
    }
  }

  // 验证邮箱
  static async verifyEmail(verificationToken) {
    const sql = `
      UPDATE user_emails 
      SET 
        is_verified = 1,
        verified_at = ?,
        verification_token = NULL,
        updated_time = ?
      WHERE verification_token = ? AND is_active = 1
    `;

    const now = new Date();
    try {
      const result = await query(sql, [now, now, verificationToken]);
      if (result.affectedRows > 0) {
        // 查找并返回验证后的邮箱信息
        const findSql = 'SELECT * FROM user_emails WHERE verified_at = ? ORDER BY verified_at DESC LIMIT 1';
        const userEmails = await query(findSql, [now]);
        return userEmails[0] || null;
      }
      return null;
    } catch (error) {
      console.error('验证邮箱失败:', error.message);
      throw error;
    }
  }
}

module.exports = UserEmail;
