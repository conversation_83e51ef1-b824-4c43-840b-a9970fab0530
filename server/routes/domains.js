const express = require('express');
const Domain = require('../models/Domain');
const { authMiddleware } = require('../middleware/auth');
const { hasPermission } = require('../middleware/permission');
const router = express.Router();

// 获取所有域名 - 需要域名查询权限
router.get('/', authMiddleware, hasPermission('domain:query'), async (req, res) => {
  try {
    const { page = 1, pageSize = 10, domain_name, status } = req.query;

    // 构建筛选条件
    const filters = {};
    if (domain_name) filters.domain_name = domain_name;
    if (status !== undefined && status !== '') filters.status = parseInt(status);

    const result = await Domain.findAll(parseInt(page), parseInt(pageSize), filters);

    res.json({
      code: 200,
      data: result.list,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      totalPages: result.totalPages,
      message: '获取域名列表成功'
    });
  } catch (error) {
    console.error('获取域名列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取域名列表失败'
    });
  }
});

// 根据ID获取域名 - 需要域名查询权限
router.get('/:id', authMiddleware, hasPermission('domain:query'), async (req, res) => {
  try {
    const { id } = req.params;
    const domain = await Domain.findById(id);

    if (!domain) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '域名不存在'
      });
    }

    res.json({
      code: 200,
      data: domain,
      message: '获取域名信息成功'
    });
  } catch (error) {
    console.error('获取域名信息失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取域名信息失败'
    });
  }
});

// 创建域名 - 需要域名添加权限
router.post('/', authMiddleware, hasPermission('domain:add'), async (req, res) => {
  try {
    const { domain_name, status, description } = req.body;

    // 添加详细日志
    console.log('📝 创建域名请求数据:', {
      domain_name,
      status,
      description
    });

    // 简单验证
    if (!domain_name) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '域名名称是必填项'
      });
    }

    // 域名格式验证
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!domainRegex.test(domain_name)) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '域名格式不正确'
      });
    }

    // 检查域名是否已存在
    const existingDomain = await Domain.findByDomainName(domain_name);
    if (existingDomain) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '域名已存在'
      });
    }

    const domain = await Domain.create({
      domain_name,
      status: status !== undefined ? status : 1,
      description
    });

    res.status(200).json({
      code: 200,
      data: domain,
      message: '域名创建成功'
    });
  } catch (error) {
    console.error('创建域名失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '创建域名失败'
    });
  }
});

// 更新域名 - 需要域名更新权限
router.put('/:id', authMiddleware, hasPermission('domain:update'), async (req, res) => {
  try {
    const { id } = req.params;
    const { domain_name, status, description } = req.body;

    if (!domain_name) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '域名名称是必填项'
      });
    }

    // 域名格式验证
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!domainRegex.test(domain_name)) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '域名格式不正确'
      });
    }

    // 检查域名是否已被其他记录使用
    const existingDomain = await Domain.findByDomainName(domain_name);
    if (existingDomain && existingDomain.id !== id) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '域名已被其他记录使用'
      });
    }

    const domain = await Domain.update(id, {
      domain_name,
      status: status !== undefined ? status : 1,
      description
    });

    if (!domain) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '域名不存在'
      });
    }

    res.json({
      code: 200,
      data: domain,
      message: '域名更新成功'
    });
  } catch (error) {
    console.error('更新域名失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '更新域名失败'
    });
  }
});

// 删除域名 - 需要域名删除权限
router.delete('/:id', authMiddleware, hasPermission('domain:delete'), async (req, res) => {
  try {
    const { id } = req.params;
    const success = await Domain.delete(id);

    if (!success) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '域名不存在'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '域名删除成功'
    });
  } catch (error) {
    console.error('删除域名失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '删除域名失败'
    });
  }
});

// 批量删除域名 - 需要域名删除权限
router.delete('/', authMiddleware, hasPermission('domain:delete'), async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '请提供要删除的域名ID列表'
      });
    }

    const success = await Domain.batchDelete(ids);

    if (!success) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '批量删除失败'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '批量删除成功'
    });
  } catch (error) {
    console.error('批量删除域名失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '批量删除域名失败'
    });
  }
});

// 更新域名状态 - 需要域名更新权限
router.patch('/:id/status', authMiddleware, hasPermission('domain:update'), async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (status === undefined || (status !== 0 && status !== 1)) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '状态值必须是0（停用）或1（启用）'
      });
    }

    const domain = await Domain.updateStatus(id, status);

    if (!domain) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '域名不存在'
      });
    }

    res.json({
      code: 200,
      data: domain,
      message: `域名${status === 1 ? '启用' : '停用'}成功`
    });
  } catch (error) {
    console.error('更新域名状态失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '更新域名状态失败'
    });
  }
});

// 获取启用的域名列表 - 需要域名查询权限
router.get('/enabled/list', authMiddleware, hasPermission('domain:query'), async (req, res) => {
  try {
    const domains = await Domain.findEnabled();

    res.json({
      code: 200,
      data: domains,
      message: '获取启用域名列表成功'
    });
  } catch (error) {
    console.error('获取启用域名列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取启用域名列表失败'
    });
  }
});

module.exports = router;
