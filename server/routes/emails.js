const express = require('express');
const router = express.Router();
const emailService = require('../services/emailService');

// 获取邮件列表
router.get('/', async (req, res) => {
  try {
    const options = {
      page: req.query.page,
      pageSize: req.query.pageSize,
      fromAddress: req.query.fromAddress,
      toAddress: req.query.toAddress,
      subject: req.query.subject,
      verificationCode: req.query.verificationCode,
      startTime: req.query.startTime,
      endTime: req.query.endTime,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await emailService.getEmailList(options);
    
    res.json({
      code: 200,
      message: '获取邮件列表成功',
      data: result
    });
  } catch (error) {
    console.error('获取邮件列表失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 根据ID获取邮件详情
router.get('/:id', async (req, res) => {
  try {
    const email = await emailService.getEmailById(req.params.id);
    if (!email) {
      return res.status(404).json({
        code: 404,
        message: '邮件不存在',
        data: null
      });
    }

    res.json({
      code: 200,
      message: '获取邮件详情成功',
      data: email
    });
  } catch (error) {
    console.error('获取邮件详情失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 创建邮件
router.post('/', async (req, res) => {
  try {
    const email = await emailService.createEmail(req.body);
    
    res.status(201).json({
      code: 201,
      message: '创建邮件成功',
      data: email
    });
  } catch (error) {
    console.error('创建邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 更新邮件
router.put('/:id', async (req, res) => {
  try {
    const email = await emailService.updateEmail(req.params.id, req.body);
    
    res.json({
      code: 200,
      message: '更新邮件成功',
      data: email
    });
  } catch (error) {
    console.error('更新邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 删除邮件
router.delete('/:id', async (req, res) => {
  try {
    await emailService.deleteEmail(req.params.id);
    
    res.json({
      code: 200,
      message: '删除邮件成功',
      data: null
    });
  } catch (error) {
    console.error('删除邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 批量删除邮件
router.delete('/', async (req, res) => {
  try {
    const { ids } = req.body;
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请提供要删除的邮件ID数组',
        data: null
      });
    }

    const deletedCount = await emailService.deleteEmails(ids);
    
    res.json({
      code: 200,
      message: `成功删除${deletedCount}封邮件`,
      data: { deletedCount }
    });
  } catch (error) {
    console.error('批量删除邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 获取邮件统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const stats = await emailService.getEmailStats();
    
    res.json({
      code: 200,
      message: '获取邮件统计成功',
      data: stats
    });
  } catch (error) {
    console.error('获取邮件统计失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

module.exports = router;
