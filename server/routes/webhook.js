const express = require('express');
const { PostalMime } = require('postal-mime');
const Email = require('../models/Email');
const router = express.Router();

// 邮件接收 Webhook 端点
router.post('/email', async (req, res) => {
  console.log('\n🔔 ===== 收到新邮件 =====');
  console.log('📤 发件人:', req.body.from);
  console.log('📥 收件人:', req.body.to);
  console.log('📝 主题:', req.body.subject);

  try {
    // 专业的邮件解码处理
    if (req.body.rawContent) {
      console.log('\n🔧 开始专业邮件解码...');

      try {
        // 步骤1: ArrayBuffer 读取 - 将原始内容转换为 ArrayBuffer
        const rawEmailString = req.body.rawContent;
        const encoder = new TextEncoder();
        const arrayBuffer = encoder.encode(rawEmailString).buffer;

        console.log('✅ 步骤1: ArrayBuffer 读取完成，大小:', arrayBuffer.byteLength, '字节');

        // 步骤2: TextDecoder 解码 - 将二进制数据解码为 UTF-8 字符串
        const decoder = new TextDecoder('utf-8');
        const uint8Array = new Uint8Array(arrayBuffer);
        const decodedString = decoder.decode(uint8Array);

        console.log('✅ 步骤2: TextDecoder 解码完成，长度:', decodedString.length, '字符');

        // 步骤3: postal-mime 解析 - 专业的邮件解析库
        const parser = new PostalMime();
        const parsedEmail = await parser.parse(decodedString);

        console.log('✅ 步骤3: postal-mime 解析完成');

        // 提取解析后的内容
        const emailText = parsedEmail.text || '';
        const emailHtml = parsedEmail.html || '';
        const subject = parsedEmail.subject || '';

        console.log('\n📊 解析结果:');
        console.log('📝 主题:', subject);
        console.log('📄 文本长度:', emailText.length);
        console.log('🌐 HTML长度:', emailHtml.length);

        // 准备存储到数据库的内容
        let htmlContentForDB = '';

        // 优先使用HTML内容，如果没有则使用纯文本（转换为HTML格式）
        if (emailHtml) {
          htmlContentForDB = emailHtml;
          console.log('\n🌐 HTML邮件内容（保留标签用于iframe）:');
          console.log('='.repeat(60));
          console.log(emailHtml);
          console.log('='.repeat(60));
        } else if (emailText) {
          // 将纯文本转换为HTML格式存储
          htmlContentForDB = `<pre style="white-space: pre-wrap; font-family: Arial, sans-serif;">${emailText}</pre>`;
          console.log('\n📄 纯文本内容（将转换为HTML存储）:');
          console.log('-'.repeat(60));
          console.log(emailText);
          console.log('-'.repeat(60));
        }

        // 从HTML或文本中提取验证码（需要去除HTML标签）
        const contentForCodeExtraction = emailText || emailHtml;
        if (contentForCodeExtraction) {
          // 从HTML中移除标签来提取验证码
          const cleanTextForCode = contentForCodeExtraction
            .replace(/<[^>]*>/g, '') // 移除HTML标签
            .replace(/&nbsp;/g, ' ') // 替换HTML实体
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"');

          const codePatterns = [
            /验证码为【(\d+)】/,
            /验证码为(\d+)/,
            /验证码[：:]\s*(\d+)/,
            /验证码.*?(\d{6})/,
            /【(\d{6})】/,
            /(\d{6})/
          ];

          let extractedVerificationCode = '';
          for (const pattern of codePatterns) {
            const match = cleanTextForCode.match(pattern);
            if (match) {
              const code = match[1];
              // 验证是否为6位数字
              if (code && code.length === 6 && /^\d{6}$/.test(code)) {
                extractedVerificationCode = code;
                break;
              }
            }
          }
          if (extractedVerificationCode) {
            console.log('\n🔑 验证码:', extractedVerificationCode);
          }
        }

        // 保存邮件到数据库
        try {
          // 格式化日期时间为MySQL兼容格式
          const formatDateTime = (dateInput) => {
            if (!dateInput) return null;
            const date = new Date(dateInput);
            return date.toISOString().slice(0, 19).replace('T', ' ');
          };

          const emailData = {
            message_id: req.body.messageId,
            from_address: req.body.from,
            to_address: req.body.to,
            subject: subject,
            sent_time: formatDateTime(req.body.date),
            received_time: formatDateTime(req.body.receivedAt),
            html_content: htmlContentForDB,
            verification_code: extractedVerificationCode || null
          };

          // 检查邮件是否已存在
          const existingEmail = await Email.findByMessageId(req.body.messageId);
          if (existingEmail) {
            console.log('\n⚠️ 邮件已存在，跳过保存');
          } else {
            const savedEmail = await Email.create(emailData);
            console.log('\n✅ 邮件已保存到数据库，ID:', savedEmail.id);
          }
        } catch (dbError) {
          console.error('\n❌ 保存邮件到数据库失败:', dbError.message);
        }
      } catch (parseError) {
        console.error('❌ 邮件解析失败:', parseError.message);
        console.log('🔄 回退到简单解码方式...');

        // 回退到简单的解码方式
        const fallbackContent = req.body.textContent || req.body.htmlContent || '';
        let fallbackHtmlContent = '';
        let fallbackVerificationCode = null;

        if (fallbackContent) {
          const cleanText = fallbackContent
              .replace(/<[^>]*>/g, '')
              .replace(/&nbsp;/g, ' ')
              .replace(/&amp;/g, '&')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"')
              .replace(/\s+/g, ' ')
              .trim();

          console.log('\n📄 回退解码后的邮件正文:');
          console.log('='.repeat(60));
          console.log(cleanText);
          console.log('='.repeat(60));

          // 设置回退的HTML内容
          fallbackHtmlContent = req.body.htmlContent || `<pre style="white-space: pre-wrap; font-family: Arial, sans-serif;">${cleanText}</pre>`;

          // 尝试从回退内容中提取验证码
          const codePatterns = [
            /验证码为【(\d+)】/,
            /验证码为(\d+)/,
            /验证码[：:]\s*(\d+)/,
            /验证码.*?(\d{6})/,
            /【(\d{6})】/,
            /(\d{6})/
          ];

          for (const pattern of codePatterns) {
            const match = cleanText.match(pattern);
            if (match) {
              const code = match[1];
              if (code && code.length === 6 && /^\d{6}$/.test(code)) {
                fallbackVerificationCode = code;
                console.log('\n🔑 回退方式提取的验证码:', fallbackVerificationCode);
                break;
              }
            }
          }
        }

        // 即使解析失败，也要保存邮件到数据库
        try {
          const formatDateTime = (dateInput) => {
            if (!dateInput) return null;
            const date = new Date(dateInput);
            return date.toISOString().slice(0, 19).replace('T', ' ');
          };

          const fallbackEmailData = {
            message_id: req.body.messageId,
            from_address: req.body.from,
            to_address: req.body.to,
            subject: req.body.subject || '(解析失败的邮件)',
            sent_time: formatDateTime(req.body.date),
            received_time: formatDateTime(req.body.receivedAt),
            html_content: fallbackHtmlContent,
            verification_code: fallbackVerificationCode || req.body.verificationCode || null
          };

          // 检查邮件是否已存在
          const existingEmail = await Email.findByMessageId(req.body.messageId);
          if (existingEmail) {
            console.log('\n⚠️ 邮件已存在，跳过保存');
          } else {
            const savedEmail = await Email.create(fallbackEmailData);
            console.log('\n✅ 回退方式保存邮件成功，ID:', savedEmail.id);
          }
        } catch (fallbackDbError) {
          console.error('\n❌ 回退方式保存邮件失败:', fallbackDbError.message);
        }
      }
    } else {
      // 处理没有rawContent的情况
      console.log('\n📧 没有原始邮件内容，使用基础信息保存...');

      try {
        const formatDateTime = (dateInput) => {
          if (!dateInput) return null;
          const date = new Date(dateInput);
          return date.toISOString().slice(0, 19).replace('T', ' ');
        };

        const basicEmailData = {
          message_id: req.body.messageId,
          from_address: req.body.from,
          to_address: req.body.to,
          subject: req.body.subject || '',
          sent_time: formatDateTime(req.body.date),
          received_time: formatDateTime(req.body.receivedAt),
          html_content: req.body.htmlContent || req.body.textContent || '',
          verification_code: req.body.verificationCode || null
        };

        // 检查邮件是否已存在
        const existingEmail = await Email.findByMessageId(req.body.messageId);
        if (existingEmail) {
          console.log('\n⚠️ 邮件已存在，跳过保存');
        } else {
          const savedEmail = await Email.create(basicEmailData);
          console.log('\n✅ 基础信息保存邮件成功，ID:', savedEmail.id);
        }
      } catch (basicDbError) {
        console.error('\n❌ 基础信息保存邮件失败:', basicDbError.message);
      }
    }

    // 显示Worker提取的验证码（备用）
    if (req.body.verificationCode) {
      console.log('\n🔑 Worker提取的验证码:', req.body.verificationCode);
    }

    console.log('\n✅ 邮件处理完成');

    // 返回成功响应
    res.json({
      code: 200,
      message: '邮件接收成功',
      data: {
        messageId: req.body.messageId,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ 处理邮件时发生错误:', error);
    res.status(500).json({
      code: 500,
      message: '邮件处理失败',
      data: null
    });
  }
});

// 测试端点
router.get('/test', (req, res) => {
  res.json({
    code: 200,
    message: 'Webhook 服务正常运行',
    data: {
      timestamp: new Date().toISOString(),
      server: 'MailCode Webhook Server',
      version: '1.0.0',
      endpoints: {
        email: 'POST /api/webhook/email',
        test: 'GET /api/webhook/test'
      }
    }
  });
});

module.exports = router;
